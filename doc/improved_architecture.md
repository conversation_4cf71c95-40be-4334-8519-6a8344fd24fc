# 用户中心系统架构优化改进方案

## 项目概述

本方案基于当前用户中心项目的实际技术栈，引入RocketMQ消息队列和阿里云OSS对象存储，构建高并发、高可用的简历批量处理系统。主要技术栈包括：
- **框架**: Spring Boot 3.2.0 + MyBatis Plus
- **数据库**: MySQL 8.0 + Redis
- **消息队列**: RocketMQ 2.2.3
- **对象存储**: 阿里云OSS
- **服务发现**: Nacos 2.1.2
- **监控**: Prometheus + Micrometer + SkyWalking
- **核心功能**: 简历批量上传解析、用户管理

## 架构升级目标

1. **引入RocketMQ**: 实现异步解耦，提升系统吞吐量
2. **集成阿里云OSS**: 解决文件存储和管理问题
3. **优化线程池**: 提升并发处理能力
4. **增强监控**: 完善系统可观测性
5. **提升性能**: 批量处理性能提升50%+

## 一、线程池优化配置

### 1.1 整合现有线程池配置

基于当前项目的AsyncTaskExecutorConfig和AsyncConfig，优化线程池设计：

```java
@Configuration
@EnableAsync
@ConfigurationProperties(prefix = "async.task.executor")
@Slf4j
public class OptimizedThreadPoolConfig {

    // 通用异步任务线程池（整合现有taskExecutor）
    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(15); // 降低最大线程数，避免过度竞争
        executor.setQueueCapacity(200); // 增加队列容量
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("AsyncTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    // 简历解析专用线程池（优化现有batchResumeParseExecutor）
    @Bean("batchResumeParseExecutor")
    public ThreadPoolTaskExecutor batchResumeParseExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3); // I/O密集型，适中的核心线程数
        executor.setMaxPoolSize(8); // 降低最大线程数
        executor.setQueueCapacity(100); // 有界队列防止OOM
        executor.setKeepAliveSeconds(300);
        executor.setThreadNamePrefix("BatchResumeParser-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        executor.initialize();
        return executor;
    }

    // 第三方API调用专用线程池
    @Bean("apiCallExecutor")
    public ThreadPoolTaskExecutor apiCallExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2); // 匹配第三方API限流
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("ApiCall-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    // 缓存预热专用线程池（保留现有配置）
    @Bean("cacheWarmupExecutor")
    public ThreadPoolTaskExecutor cacheWarmupExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("CacheWarmup-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }
}
```

### 1.2 线程池监控和指标收集

基于现有的Micrometer监控体系，增强线程池监控：

```java
@Component
@Slf4j
public class ThreadPoolMonitorService {

    private final MeterRegistry meterRegistry;
    private final Map<String, ThreadPoolTaskExecutor> threadPools;

    public ThreadPoolMonitorService(MeterRegistry meterRegistry,
                                   @Qualifier("taskExecutor") ThreadPoolTaskExecutor taskExecutor,
                                   @Qualifier("batchResumeParseExecutor") ThreadPoolTaskExecutor batchExecutor,
                                   @Qualifier("apiCallExecutor") ThreadPoolTaskExecutor apiExecutor) {
        this.meterRegistry = meterRegistry;
        this.threadPools = Map.of(
            "taskExecutor", taskExecutor,
            "batchResumeParseExecutor", batchExecutor,
            "apiCallExecutor", apiExecutor
        );

        // 注册线程池指标
        registerThreadPoolMetrics();
    }

    private void registerThreadPoolMetrics() {
        threadPools.forEach((name, executor) -> {
            // 活跃线程数
            Gauge.builder("threadpool.active.threads")
                .tag("pool", name)
                .register(meterRegistry, executor, e -> e.getActiveCount());

            // 队列大小
            Gauge.builder("threadpool.queue.size")
                .tag("pool", name)
                .register(meterRegistry, executor, e -> e.getQueueSize());

            // 线程池大小
            Gauge.builder("threadpool.pool.size")
                .tag("pool", name)
                .register(meterRegistry, executor, e -> e.getPoolSize());
        });
    }

    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void logThreadPoolStatus() {
        threadPools.forEach((name, executor) -> {
            log.info("线程池状态 [{}]: 活跃={}, 队列={}, 池大小={}, 最大={}",
                name, executor.getActiveCount(), executor.getQueueSize(),
                executor.getPoolSize(), executor.getMaxPoolSize());
        });
    }
}

## 二、RocketMQ消息队列集成

### 2.1 RocketMQ配置

首先添加RocketMQ相关配置：

```yaml
# application.yml
rocketmq:
  name-server: 127.0.0.1:9876  # 生产环境需要配置集群地址
  producer:
    group: user-center-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304  # 4MB
    compress-message-body-threshold: 4096
  consumer:
    group: user-center-consumer-group
    consume-thread-min: 5
    consume-thread-max: 20
    consume-message-batch-max-size: 1
```

### 2.2 RocketMQ生产者优化配置

```java
@Configuration
@EnableConfigurationProperties(RocketMQProperties.class)
@Slf4j
public class RocketMQProducerConfig {

    @Bean
    public DefaultMQProducer resumeParseProducer() {
        DefaultMQProducer producer = new DefaultMQProducer("resume-parse-producer-group");
        producer.setNamesrvAddr("127.0.0.1:9876"); // 从配置文件读取

        // 优化配置
        producer.setRetryTimesWhenSendFailed(2); // 减少重试次数
        producer.setSendMsgTimeout(5000); // 5秒超时
        producer.setMaxMessageSize(4 * 1024 * 1024); // 4MB限制

        // 批量发送配置
        producer.setDefaultTopicQueueNums(4); // 默认队列数

        try {
            producer.start();
            log.info("RocketMQ简历解析生产者启动成功");
        } catch (MQClientException e) {
            log.error("RocketMQ简历解析生产者启动失败", e);
            throw new RuntimeException("RocketMQ生产者启动失败", e);
        }

        return producer;
    }

    @Bean
    public DefaultMQProducer fileUploadProducer() {
        DefaultMQProducer producer = new DefaultMQProducer("file-upload-producer-group");
        producer.setNamesrvAddr("127.0.0.1:9876");

        // 文件上传专用配置
        producer.setRetryTimesWhenSendFailed(3);
        producer.setSendMsgTimeout(10000); // 文件上传允许更长超时
        producer.setMaxMessageSize(1024); // 只传递文件元信息，不传文件内容

        try {
            producer.start();
            log.info("RocketMQ文件上传生产者启动成功");
        } catch (MQClientException e) {
            log.error("RocketMQ文件上传生产者启动失败", e);
            throw new RuntimeException("RocketMQ生产者启动失败", e);
        }

        return producer;
    }
}
```

### 2.3 消息生产服务

```java
@Service
@Slf4j
public class MessageProducerService {

    private final DefaultMQProducer resumeParseProducer;
    private final DefaultMQProducer fileUploadProducer;
    private final RateLimiter producerLimiter;
    private final MeterRegistry meterRegistry;

    // 主题定义
    public static final String RESUME_PARSE_TOPIC = "RESUME_PARSE_TOPIC";
    public static final String FILE_UPLOAD_TOPIC = "FILE_UPLOAD_TOPIC";

    public MessageProducerService(@Qualifier("resumeParseProducer") DefaultMQProducer resumeParseProducer,
                                 @Qualifier("fileUploadProducer") DefaultMQProducer fileUploadProducer,
                                 MeterRegistry meterRegistry) {
        this.resumeParseProducer = resumeParseProducer;
        this.fileUploadProducer = fileUploadProducer;
        this.meterRegistry = meterRegistry;
        // 限制生产速度，避免消息积压
        this.producerLimiter = RateLimiter.create(50.0); // 50条消息/秒
    }

    /**
     * 发送简历解析消息
     */
    public void sendResumeParseMessage(ResumeParseMessage message) {
        if (!producerLimiter.tryAcquire()) {
            throw new RateLimitExceededException("消息发送频率过高，请稍后重试");
        }

        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            Message mqMessage = new Message(
                RESUME_PARSE_TOPIC,
                "PARSE", // Tag
                message.getMessageId(),
                JSON.toJSONBytes(message)
            );

            // 设置消息属性
            mqMessage.putUserProperty("batchId", message.getBatchId());
            mqMessage.putUserProperty("fileType", message.getFileType());
            mqMessage.putUserProperty("priority", String.valueOf(message.getPriority()));

            // 异步发送
            resumeParseProducer.send(mqMessage, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.debug("简历解析消息发送成功: messageId={}, queueId={}",
                            message.getMessageId(), sendResult.getMessageQueue().getQueueId());
                    meterRegistry.counter("mq.message.send.success",
                        "topic", RESUME_PARSE_TOPIC).increment();
                }

                @Override
                public void onException(Throwable e) {
                    log.error("简历解析消息发送失败: messageId={}", message.getMessageId(), e);
                    meterRegistry.counter("mq.message.send.failure",
                        "topic", RESUME_PARSE_TOPIC).increment();
                    // 可以实现重试或死信队列逻辑
                }
            });

        } catch (Exception e) {
            log.error("发送简历解析消息异常: messageId={}", message.getMessageId(), e);
            meterRegistry.counter("mq.message.send.error",
                "topic", RESUME_PARSE_TOPIC).increment();
            throw new MessageSendException("消息发送失败", e);
        } finally {
            sample.stop(Timer.builder("mq.message.send.duration")
                .tag("topic", RESUME_PARSE_TOPIC)
                .register(meterRegistry));
        }
    }

    /**
     * 发送文件上传消息
     */
    public void sendFileUploadMessage(FileUploadMessage message) {
        try {
            Message mqMessage = new Message(
                FILE_UPLOAD_TOPIC,
                "UPLOAD",
                message.getMessageId(),
                JSON.toJSONBytes(message)
            );

            mqMessage.putUserProperty("fileSize", String.valueOf(message.getFileSize()));
            mqMessage.putUserProperty("fileType", message.getFileType());

            SendResult result = fileUploadProducer.send(mqMessage);
            log.debug("文件上传消息发送成功: messageId={}, result={}",
                    message.getMessageId(), result.getSendStatus());

            meterRegistry.counter("mq.message.send.success",
                "topic", FILE_UPLOAD_TOPIC).increment();

        } catch (Exception e) {
            log.error("发送文件上传消息失败: messageId={}", message.getMessageId(), e);
            meterRegistry.counter("mq.message.send.failure",
                "topic", FILE_UPLOAD_TOPIC).increment();
            throw new MessageSendException("文件上传消息发送失败", e);
        }
    }
}

### 2.4 RocketMQ消费者配置

```java
@Component
@Slf4j
public class ResumeParseMessageConsumer {

    private final ThirdPartyResumeParseService parseService;
    private final OSSFileStorageService ossService;
    private final RateLimiter consumerLimiter;
    private final CircuitBreaker apiCircuitBreaker;
    private final MeterRegistry meterRegistry;

    public ResumeParseMessageConsumer(ThirdPartyResumeParseService parseService,
                                    OSSFileStorageService ossService,
                                    MeterRegistry meterRegistry) {
        this.parseService = parseService;
        this.ossService = ossService;
        this.meterRegistry = meterRegistry;
        this.consumerLimiter = RateLimiter.create(30.0); // 30条消息/秒
        this.apiCircuitBreaker = CircuitBreaker.custom("third-party-api")
            .failureRateThreshold(50)
            .waitDurationInOpenState(Duration.ofSeconds(30))
            .slidingWindowSize(20)
            .minimumNumberOfCalls(10)
            .build();
    }

    @RocketMQMessageListener(
        topic = "RESUME_PARSE_TOPIC",
        consumerGroup = "resume-parse-consumer-group",
        consumeMode = ConsumeMode.ORDERLY, // 顺序消费
        consumeThreadMax = 10,
        maxReconsumeTimes = 3
    )
    public class ResumeParseListener implements RocketMQListener<ResumeParseMessage> {

        @Override
        public void onMessage(ResumeParseMessage message) {
            Timer.Sample sample = Timer.start(meterRegistry);

            try {
                // 限流控制
                if (!consumerLimiter.tryAcquire()) {
                    log.warn("消费限流触发，延迟处理消息: {}", message.getMessageId());
                    Thread.sleep(1000); // 简单延迟，实际可以使用更复杂的策略
                }

                log.info("开始处理简历解析消息: messageId={}, batchId={}",
                        message.getMessageId(), message.getBatchId());

                // 从OSS下载文件
                byte[] fileContent = ossService.downloadFile(message.getOssKey());

                // 使用熔断器保护第三方API调用
                ThirdPartyParseResultDTO parseResult = apiCircuitBreaker.executeSupplier(() -> {
                    return parseService.parseResumeFromBytes(fileContent, message.getFileName());
                });

                // 处理解析结果
                processParseResult(message, parseResult);

                meterRegistry.counter("mq.message.consume.success",
                    "topic", "RESUME_PARSE_TOPIC").increment();

                log.info("简历解析消息处理完成: messageId={}", message.getMessageId());

            } catch (CallNotPermittedException e) {
                log.warn("第三方API熔断，消息将重试: messageId={}", message.getMessageId());
                meterRegistry.counter("mq.message.consume.circuit_breaker",
                    "topic", "RESUME_PARSE_TOPIC").increment();
                throw new RuntimeException("API熔断，触发重试", e);

            } catch (Exception e) {
                log.error("处理简历解析消息失败: messageId={}", message.getMessageId(), e);
                meterRegistry.counter("mq.message.consume.failure",
                    "topic", "RESUME_PARSE_TOPIC").increment();

                // 根据错误类型决定是否重试
                if (isRetryableError(e)) {
                    throw e; // 触发重试
                } else {
                    // 不可重试错误，记录到死信队列
                    handleNonRetryableError(message, e);
                }
            } finally {
                sample.stop(Timer.builder("mq.message.consume.duration")
                    .tag("topic", "RESUME_PARSE_TOPIC")
                    .register(meterRegistry));
            }
        }

        private void processParseResult(ResumeParseMessage message, ThirdPartyParseResultDTO parseResult) {
            // 保存解析结果到数据库
            // 更新批量处理状态
            // 发送处理完成通知等
        }

        private boolean isRetryableError(Exception e) {
            return e instanceof SocketTimeoutException ||
                   e instanceof ConnectException ||
                   e instanceof HttpServerErrorException;
        }

        private void handleNonRetryableError(ResumeParseMessage message, Exception e) {
            // 记录到死信队列或错误日志
            log.error("不可重试错误，消息处理失败: messageId={}", message.getMessageId(), e);
        }
    }
}

/**
 * 文件上传消息消费者
 */
@Component
@Slf4j
public class FileUploadMessageConsumer {

    @RocketMQMessageListener(
        topic = "FILE_UPLOAD_TOPIC",
        consumerGroup = "file-upload-consumer-group",
        consumeMode = ConsumeMode.CONCURRENTLY,
        consumeThreadMax = 5
    )
    public class FileUploadListener implements RocketMQListener<FileUploadMessage> {

        @Override
        public void onMessage(FileUploadMessage message) {
            log.info("处理文件上传消息: messageId={}, fileName={}",
                    message.getMessageId(), message.getFileName());

            // 处理文件上传后的业务逻辑
            // 例如：更新数据库状态、发送通知等
        }
    }
}

## 三、阿里云OSS集成优化

### 3.1 OSS配置和客户端优化

```yaml
# application.yml
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: ${ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
    bucket-name: user-center-files
    # 内网endpoint，提升上传下载速度
    internal-endpoint: https://oss-cn-hangzhou-internal.aliyuncs.com
    # 连接配置
    connection-timeout: 3000
    socket-timeout: 30000
    max-connections: 50
    max-error-retry: 3
```

```java
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
@Data
@Slf4j
public class OSSConfig {

    private String endpoint;
    private String internalEndpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private int connectionTimeout = 3000;
    private int socketTimeout = 30000;
    private int maxConnections = 50;
    private int maxErrorRetry = 3;

    @Bean
    public OSS ossClient() {
        // 优化的客户端配置
        ClientBuilderConfiguration config = new ClientBuilderConfiguration();
        config.setMaxConnections(maxConnections);
        config.setSocketTimeout(socketTimeout);
        config.setConnectionTimeout(connectionTimeout);
        config.setMaxErrorRetry(maxErrorRetry);

        // 启用连接池
        config.setUseConnectionPool(true);
        config.setConnectionPoolSize(maxConnections / 2);

        // 启用CRC校验
        config.setCrcCheckEnabled(true);

        OSS ossClient = new OSSClientBuilder()
            .endpoint(endpoint)
            .credentialsProvider(new DefaultCredentialProvider(accessKeyId, accessKeySecret))
            .clientConfiguration(config)
            .build();

        log.info("阿里云OSS客户端初始化完成: endpoint={}, bucket={}", endpoint, bucketName);
        return ossClient;
    }
}

### 3.2 智能OSS文件存储服务

```java
@Service
@Slf4j
public class OSSFileStorageService {

    private final OSS ossClient;
    private final OSSConfig ossConfig;
    private final ThreadPoolTaskExecutor ossUploadExecutor;
    private final LoadingCache<String, String> urlCache;
    private final MeterRegistry meterRegistry;

    public OSSFileStorageService(OSS ossClient,
                               OSSConfig ossConfig,
                               @Qualifier("ossUploadExecutor") ThreadPoolTaskExecutor executor,
                               MeterRegistry meterRegistry) {
        this.ossClient = ossClient;
        this.ossConfig = ossConfig;
        this.ossUploadExecutor = executor;
        this.meterRegistry = meterRegistry;

        // URL缓存，避免重复生成
        this.urlCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(2, TimeUnit.HOURS)
            .build();
    }

    /**
     * 智能文件上传
     */
    public CompletableFuture<String> uploadFileAsync(String fileName, byte[] content) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);

            try {
                // 计算文件MD5，实现去重
                String md5 = DigestUtils.md5Hex(content);
                String objectKey = generateObjectKey(fileName, md5);

                // 检查文件是否已存在
                if (ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey)) {
                    log.debug("文件已存在OSS中: {}", objectKey);
                    meterRegistry.counter("oss.upload.duplicate").increment();
                    return generateFileUrl(objectKey);
                }

                // 根据文件大小选择上传策略
                String fileUrl;
                if (content.length > 100 * 1024 * 1024) { // 100MB以上
                    fileUrl = multipartUploadWithRetry(objectKey, content);
                } else if (content.length > 5 * 1024 * 1024) { // 5MB以上
                    fileUrl = multipartUpload(objectKey, content);
                } else {
                    fileUrl = simpleUploadWithRetry(objectKey, content);
                }

                meterRegistry.counter("oss.upload.success").increment();
                return fileUrl;

            } catch (Exception e) {
                log.error("OSS文件上传失败: fileName={}", fileName, e);
                meterRegistry.counter("oss.upload.failure").increment();
                throw new RuntimeException("文件上传失败", e);
            } finally {
                sample.stop(Timer.builder("oss.upload.duration")
                    .tag("file_size_category", getFileSizeCategory(content.length))
                    .register(meterRegistry));
            }
        }, ossUploadExecutor);
    }

    /**
     * 简单上传（小文件）
     */
    private String simpleUploadWithRetry(String objectKey, byte[] content) {
        int maxRetries = 3;
        Exception lastException = null;

        for (int i = 0; i < maxRetries; i++) {
            try {
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(content.length);
                metadata.setContentType(getContentType(objectKey));
                metadata.setServerSideEncryption("AES256"); // 服务端加密

                // 设置缓存控制
                metadata.setCacheControl("max-age=31536000"); // 1年缓存

                PutObjectRequest request = new PutObjectRequest(
                    ossConfig.getBucketName(),
                    objectKey,
                    new ByteArrayInputStream(content),
                    metadata
                );

                ossClient.putObject(request);
                return generateFileUrl(objectKey);

            } catch (Exception e) {
                lastException = e;
                if (i < maxRetries - 1) {
                    try {
                        Thread.sleep((i + 1) * 1000); // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        throw new RuntimeException("OSS上传重试失败", lastException);
    }

    /**
     * 分片上传（大文件）
     */
    private String multipartUpload(String objectKey, byte[] content) {
        String uploadId = null;
        try {
            // 初始化分片上传
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(
                ossConfig.getBucketName(), objectKey);

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(getContentType(objectKey));
            metadata.setServerSideEncryption("AES256");
            initRequest.setObjectMetadata(metadata);

            InitiateMultipartUploadResult initResult = ossClient.initiateMultipartUpload(initRequest);
            uploadId = initResult.getUploadId();

            // 分片上传
            List<PartETag> partETags = new ArrayList<>();
            int partSize = 5 * 1024 * 1024; // 5MB分片
            int partCount = (content.length + partSize - 1) / partSize;

            for (int i = 0; i < partCount; i++) {
                int startPos = i * partSize;
                int endPos = Math.min(startPos + partSize, content.length);
                byte[] partData = Arrays.copyOfRange(content, startPos, endPos);

                UploadPartRequest uploadPartRequest = new UploadPartRequest();
                uploadPartRequest.setBucketName(ossConfig.getBucketName());
                uploadPartRequest.setKey(objectKey);
                uploadPartRequest.setUploadId(uploadId);
                uploadPartRequest.setPartNumber(i + 1);
                uploadPartRequest.setInputStream(new ByteArrayInputStream(partData));
                uploadPartRequest.setPartSize(partData.length);

                UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
                partETags.add(uploadPartResult.getPartETag());
            }

            // 完成分片上传
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(
                ossConfig.getBucketName(), objectKey, uploadId, partETags);

            ossClient.completeMultipartUpload(completeRequest);
            return generateFileUrl(objectKey);

        } catch (Exception e) {
            // 上传失败，取消分片上传
            if (uploadId != null) {
                try {
                    AbortMultipartUploadRequest abortRequest = new AbortMultipartUploadRequest(
                        ossConfig.getBucketName(), objectKey, uploadId);
                    ossClient.abortMultipartUpload(abortRequest);
                } catch (Exception abortException) {
                    log.warn("取消分片上传失败: {}", objectKey, abortException);
                }
            }
            throw new RuntimeException("分片上传失败", e);
        }
    }

    /**
     * 下载文件
     */
    public byte[] downloadFile(String objectKey) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectKey);

            try (InputStream inputStream = ossObject.getObjectContent()) {
                byte[] content = inputStream.readAllBytes();
                meterRegistry.counter("oss.download.success").increment();
                return content;
            }

        } catch (Exception e) {
            log.error("OSS文件下载失败: objectKey={}", objectKey, e);
            meterRegistry.counter("oss.download.failure").increment();
            throw new RuntimeException("文件下载失败", e);
        } finally {
            sample.stop(Timer.builder("oss.download.duration")
                .register(meterRegistry));
        }
    }

    /**
     * 生成对象键（按日期分目录）
     */
    private String generateObjectKey(String fileName, String md5) {
        LocalDate today = LocalDate.now();
        String dateDir = today.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

        String extension = getFileExtension(fileName);
        String newFileName = md5 + (extension.isEmpty() ? "" : "." + extension);

        return "resume-files/" + dateDir + "/" + newFileName;
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String objectKey) {
        String cacheKey = "url:" + objectKey;
        return urlCache.get(cacheKey, key -> {
            // 生成带签名的URL，有效期24小时
            Date expiration = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
            return ossClient.generatePresignedUrl(ossConfig.getBucketName(), objectKey, expiration).toString();
        });
    }

    /**
     * 获取文件MIME类型
     */
    private String getContentType(String objectKey) {
        String extension = getFileExtension(objectKey).toLowerCase();
        switch (extension) {
            case "pdf": return "application/pdf";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt": return "text/plain";
            case "jpg":
            case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            default: return "application/octet-stream";
        }
    }

    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    private String getFileSizeCategory(long fileSize) {
        if (fileSize < 1024 * 1024) return "small"; // < 1MB
        else if (fileSize < 10 * 1024 * 1024) return "medium"; // < 10MB
        else return "large"; // >= 10MB
    }

    private String multipartUploadWithRetry(String objectKey, byte[] content) {
        // 带重试的分片上传实现
        int maxRetries = 2;
        Exception lastException = null;

        for (int i = 0; i < maxRetries; i++) {
            try {
                return multipartUpload(objectKey, content);
            } catch (Exception e) {
                lastException = e;
                if (i < maxRetries - 1) {
                    log.warn("分片上传失败，第{}次重试: {}", i + 1, objectKey);
                    try {
                        Thread.sleep(2000 * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        throw new RuntimeException("分片上传重试失败", lastException);
    }

    /**
     * 定期清理过期的临时文件
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupExpiredFiles() {
        try {
            // 清理7天前的临时文件
            String prefix = "temp-files/";
            LocalDate cutoffDate = LocalDate.now().minusDays(7);
            String cutoffPrefix = prefix + cutoffDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

            // 列出并删除过期文件
            ListObjectsRequest listRequest = new ListObjectsRequest(ossConfig.getBucketName())
                .withPrefix(cutoffPrefix)
                .withMaxKeys(1000);

            ObjectListing objectListing = ossClient.listObjects(listRequest);

            if (!objectListing.getObjectSummaries().isEmpty()) {
                List<String> keysToDelete = objectListing.getObjectSummaries().stream()
                    .map(OSSObjectSummary::getKey)
                    .collect(Collectors.toList());

                DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(ossConfig.getBucketName())
                    .withKeys(keysToDelete);

                ossClient.deleteObjects(deleteRequest);
                log.info("清理OSS过期文件完成，删除文件数: {}", keysToDelete.size());
            }

        } catch (Exception e) {
            log.error("OSS文件清理异常", e);
        }
    }
}

## 四、错误处理和重试优化

### 4.1 基于Spring Retry的智能重试策略

集成Spring Retry框架，提供更完善的重试机制：

```java
@Component
@Slf4j
public class IntelligentRetryService {

    private final RetryTemplate retryTemplate;
    private final MeterRegistry meterRegistry;

    public IntelligentRetryService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.retryTemplate = createRetryTemplate();
    }

    private RetryTemplate createRetryTemplate() {
        return RetryTemplate.builder()
            .maxAttempts(3)
            .exponentialBackoff(1000, 2, 10000) // 1秒起始，2倍递增，最大10秒
            .retryOn(SocketTimeoutException.class, ConnectException.class,
                    HttpServerErrorException.class)
            .build();
    }

    /**
     * 执行带重试的操作
     */
    public <T> T executeWithRetry(Supplier<T> operation, String operationName) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            return retryTemplate.execute(context -> {
                if (context.getRetryCount() > 0) {
                    log.warn("操作重试: {}, 第{}次尝试", operationName, context.getRetryCount() + 1);
                    meterRegistry.counter("operation.retry", "operation", operationName).increment();
                }

                return operation.get();
            });
        } catch (Exception e) {
            log.error("操作最终失败: {}", operationName, e);
            meterRegistry.counter("operation.failure", "operation", operationName).increment();
            throw new RetryExhaustedException("重试次数耗尽: " + operationName, e);
        } finally {
            sample.stop(Timer.builder("operation.duration")
                .tag("operation", operationName)
                .register(meterRegistry));
        }
    }

    /**
     * 第三方API调用专用重试
     */
    public <T> T executeApiCallWithRetry(Supplier<T> apiCall, String apiName) {
        RetryTemplate apiRetryTemplate = RetryTemplate.builder()
            .maxAttempts(5) // API调用允许更多重试
            .exponentialBackoff(2000, 1.5, 30000) // 2秒起始，1.5倍递增，最大30秒
            .retryOn(SocketTimeoutException.class, ConnectException.class,
                    HttpServerErrorException.class, ResourceAccessException.class)
            .build();

        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            return apiRetryTemplate.execute(context -> {
                if (context.getRetryCount() > 0) {
                    log.warn("API调用重试: {}, 第{}次尝试", apiName, context.getRetryCount() + 1);
                    meterRegistry.counter("api.retry", "api", apiName).increment();
                }

                return apiCall.get();
            });
        } catch (Exception e) {
            log.error("API调用最终失败: {}", apiName, e);
            meterRegistry.counter("api.failure", "api", apiName).increment();
            throw new ApiCallException("API调用失败: " + apiName, e);
        } finally {
            sample.stop(Timer.builder("api.call.duration")
                .tag("api", apiName)
                .register(meterRegistry));
        }
    }

    /**
     * 数据库操作专用重试
     */
    public <T> T executeDatabaseOperationWithRetry(Supplier<T> dbOperation, String operationName) {
        RetryTemplate dbRetryTemplate = RetryTemplate.builder()
            .maxAttempts(2) // 数据库操作重试次数较少
            .fixedBackoff(500) // 固定500ms间隔
            .retryOn(DataAccessException.class, SQLException.class)
            .build();

        return dbRetryTemplate.execute(context -> {
            if (context.getRetryCount() > 0) {
                log.warn("数据库操作重试: {}, 第{}次尝试", operationName, context.getRetryCount() + 1);
                meterRegistry.counter("db.retry", "operation", operationName).increment();
            }

            return dbOperation.get();
        });
    }
}

### 4.2 熔断器和降级策略

基于Resilience4j实现熔断器模式：

```java
@Component
@Slf4j
public class CircuitBreakerService {

    private final CircuitBreaker thirdPartyApiCircuitBreaker;
    private final Cache<String, String> resultCache;
    private final MeterRegistry meterRegistry;

    public CircuitBreakerService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 第三方API熔断器配置
        this.thirdPartyApiCircuitBreaker = CircuitBreaker.custom("third-party-api")
            .failureRateThreshold(60) // 60%失败率触发熔断
            .waitDurationInOpenState(Duration.ofSeconds(60)) // 熔断60秒
            .slidingWindowSize(10) // 滑动窗口10次调用
            .minimumNumberOfCalls(5) // 最少5次调用才计算失败率
            .slowCallRateThreshold(50) // 50%慢调用触发熔断
            .slowCallDurationThreshold(Duration.ofSeconds(10)) // 10秒以上算慢调用
            .build();

        // 注册熔断器指标
        CircuitBreakerMetrics.ofCircuitBreaker(thirdPartyApiCircuitBreaker)
            .bindTo(meterRegistry);

        // 结果缓存
        this.resultCache = Caffeine.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .recordStats()
            .build();
    }

    /**
     * 带熔断器的第三方API调用
     */
    public <T> T callThirdPartyApiWithFallback(Supplier<T> apiCall,
                                              Function<Exception, T> fallbackFunction,
                                              String cacheKey) {
        // 先检查缓存
        if (cacheKey != null) {
            T cached = (T) resultCache.getIfPresent(cacheKey);
            if (cached != null) {
                log.debug("从缓存获取结果: {}", cacheKey);
                meterRegistry.counter("api.cache.hit").increment();
                return cached;
            }
        }

        try {
            T result = thirdPartyApiCircuitBreaker.executeSupplier(apiCall);

            // 缓存成功结果
            if (cacheKey != null && result != null) {
                resultCache.put(cacheKey, result);
                meterRegistry.counter("api.cache.put").increment();
            }

            meterRegistry.counter("api.call.success").increment();
            return result;

        } catch (CallNotPermittedException e) {
            // 熔断器开启，使用降级策略
            log.warn("第三方API熔断器开启，使用降级策略");
            meterRegistry.counter("api.circuit.breaker.fallback").increment();

            return fallbackFunction.apply(e);

        } catch (Exception e) {
            log.error("第三方API调用失败", e);
            meterRegistry.counter("api.call.failure").increment();

            return fallbackFunction.apply(e);
        }
    }

    /**
     * 简历解析API专用调用
     */
    public ThirdPartyParseResultDTO parseResumeWithFallback(MultipartFile file) {
        String cacheKey = generateCacheKey(file);

        return callThirdPartyApiWithFallback(
            () -> {
                // 实际的简历解析API调用
                return callResumeParseApi(file);
            },
            (exception) -> {
                // 降级策略：返回基础解析结果
                log.warn("简历解析API降级，返回基础结果");
                return createFallbackParseResult(file, exception);
            },
            cacheKey
        );
    }

    private String generateCacheKey(MultipartFile file) {
        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            return DigestUtils.md5Hex(content);
        } catch (Exception e) {
            return file.getOriginalFilename() + "_" + file.getSize();
        }
    }

    private ThirdPartyParseResultDTO callResumeParseApi(MultipartFile file) {
        // 实际的API调用逻辑
        // 这里应该调用现有的ThirdPartyResumeParseService
        return new ThirdPartyParseResultDTO();
    }

    private ThirdPartyParseResultDTO createFallbackParseResult(MultipartFile file, Exception e) {
        // 创建降级结果
        ThirdPartyParseResultDTO fallbackResult = new ThirdPartyParseResultDTO();
        fallbackResult.setSuccess(false);
        fallbackResult.setErrorMessage("服务暂时不可用，请稍后重试");
        return fallbackResult;
    }

    /**
     * 获取熔断器状态
     */
    public CircuitBreaker.State getCircuitBreakerState() {
        return thirdPartyApiCircuitBreaker.getState();
    }

    /**
     * 手动重置熔断器
     */
    public void resetCircuitBreaker() {
        thirdPartyApiCircuitBreaker.reset();
        log.info("熔断器已手动重置");
    }
}

## 五、数据库优化

### 5.1 MyBatis Plus配置优化

基于现有的MyBatis Plus配置，提供数据库性能优化方案：

```java
@Configuration
public class OptimizedDatabaseConfig {

    /**
     * 优化的MyBatis Plus配置
     */
    @Bean
    public MybatisPlusInterceptor optimizedMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        paginationInterceptor.setMaxLimit(1000L); // 限制最大查询数量
        paginationInterceptor.setOverflow(false); // 不允许超出总页数
        interceptor.addInnerInterceptor(paginationInterceptor);

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // SQL性能分析插件（仅开发环境）
        if (isDevelopmentEnvironment()) {
            IllegalSQLInnerInterceptor illegalSQLInterceptor = new IllegalSQLInnerInterceptor();
            interceptor.addInnerInterceptor(illegalSQLInterceptor);
        }

        return interceptor;
    }

    /**
     * 数据源配置优化
     */
    @Bean
    @ConfigurationProperties("spring.datasource.hikari")
    public HikariConfig hikariConfig() {
        HikariConfig config = new HikariConfig();

        // 连接池优化配置
        config.setMaximumPoolSize(20); // 最大连接数
        config.setMinimumIdle(5); // 最小空闲连接数
        config.setConnectionTimeout(30000); // 连接超时30秒
        config.setIdleTimeout(600000); // 空闲超时10分钟
        config.setMaxLifetime(1800000); // 连接最大生命周期30分钟
        config.setLeakDetectionThreshold(60000); // 连接泄漏检测阈值1分钟

        // 性能优化配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");

        return config;
    }

    private boolean isDevelopmentEnvironment() {
        // 检查是否为开发环境
        return Arrays.asList(environment.getActiveProfiles()).contains("local");
    }
}

### 5.2 批量操作优化

基于MyBatis Plus的批量操作优化：

```java
@Service
@Slf4j
public class OptimizedBatchDatabaseService {

    private final SqlSessionFactory sqlSessionFactory;
    private final MeterRegistry meterRegistry;

    // 批量操作大小
    private static final int BATCH_SIZE = 500;

    public OptimizedBatchDatabaseService(SqlSessionFactory sqlSessionFactory,
                                       MeterRegistry meterRegistry) {
        this.sqlSessionFactory = sqlSessionFactory;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 批量插入简历解析结果
     */
    @Async("batchResumeParseExecutor")
    public CompletableFuture<Integer> batchInsertParseResults(List<ResumeParseRecords> records) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            int totalInserted = 0;

            // 分批处理，避免事务过大
            List<List<ResumeParseRecords>> batches = Lists.partition(records, BATCH_SIZE);

            for (List<ResumeParseRecords> batch : batches) {
                int inserted = batchInsertWithOptimization(batch);
                totalInserted += inserted;

                log.debug("批量插入完成: {} 条记录", inserted);
            }

            meterRegistry.counter("database.batch.insert.success",
                "table", "resume_parse_records").increment(totalInserted);

            return CompletableFuture.completedFuture(totalInserted);

        } catch (Exception e) {
            log.error("批量插入失败", e);
            meterRegistry.counter("database.batch.insert.failure",
                "table", "resume_parse_records").increment();
            throw e;
        } finally {
            sample.stop(Timer.builder("database.batch.insert.duration")
                .tag("table", "resume_parse_records")
                .register(meterRegistry));
        }
    }

    /**
     * 优化的批量插入实现
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertWithOptimization(List<ResumeParseRecords> records) {
        if (records.isEmpty()) {
            return 0;
        }

        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            ResumeParseRecordsMapper mapper = sqlSession.getMapper(ResumeParseRecordsMapper.class);

            for (ResumeParseRecords record : records) {
                mapper.insert(record);
            }

            sqlSession.flushStatements();
            return records.size();
        }
    }

    /**
     * 批量更新用户信息
     */
    @Async("taskExecutor")
    public CompletableFuture<Integer> batchUpdateUserProfiles(List<UserProfile> profiles) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            int totalUpdated = 0;
            List<List<UserProfile>> batches = Lists.partition(profiles, BATCH_SIZE);

            for (List<UserProfile> batch : batches) {
                int updated = batchUpdateWithOptimization(batch);
                totalUpdated += updated;
            }

            meterRegistry.counter("database.batch.update.success",
                "table", "user_profile").increment(totalUpdated);

            return CompletableFuture.completedFuture(totalUpdated);

        } catch (Exception e) {
            log.error("批量更新失败", e);
            meterRegistry.counter("database.batch.update.failure",
                "table", "user_profile").increment();
            throw e;
        } finally {
            sample.stop(Timer.builder("database.batch.update.duration")
                .tag("table", "user_profile")
                .register(meterRegistry));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateWithOptimization(List<UserProfile> profiles) {
        if (profiles.isEmpty()) {
            return 0;
        }

        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            UserProfileMapper mapper = sqlSession.getMapper(UserProfileMapper.class);

            for (UserProfile profile : profiles) {
                mapper.updateById(profile);
            }

            sqlSession.flushStatements();
            return profiles.size();
        }
    }

    /**
     * 批量删除过期数据
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupExpiredData() {
        log.info("开始清理过期数据");

        try {
            // 清理7天前的临时解析记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);

            int deletedRecords = cleanupExpiredParseRecords(cutoffTime);
            log.info("清理过期解析记录: {} 条", deletedRecords);

            meterRegistry.counter("database.cleanup.records").increment(deletedRecords);

        } catch (Exception e) {
            log.error("数据清理失败", e);
            meterRegistry.counter("database.cleanup.failure").increment();
        }
    }

    private int cleanupExpiredParseRecords(LocalDateTime cutoffTime) {
        // 使用MyBatis Plus的条件删除
        LambdaQueryWrapper<ResumeParseRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(ResumeParseRecords::getCreatedAt, cutoffTime)
                   .eq(ResumeParseRecords::getParseStatus, 0); // 只删除失败的记录

        return resumeParseRecordsMapper.delete(queryWrapper);
    }
}

## 六、监控和运维优化

### 6.1 基于现有Prometheus体系的监控增强

基于项目现有的Prometheus + Micrometer监控体系，增强业务监控：

```java
@Component
@Slf4j
public class BusinessMetricsCollector {

    private final MeterRegistry meterRegistry;

    // 简历解析业务指标
    private final Counter resumeParseCounter;
    private final Timer resumeParseTimer;
    private final DistributionSummary fileSizeDistribution;

    // 用户操作指标
    private final Counter userOperationCounter;
    private final Timer userQueryTimer;

    // 系统资源指标
    private final Gauge threadPoolActiveThreads;
    private final Gauge redisConnectionUsage;
    private final Gauge databaseConnectionUsage;

    public BusinessMetricsCollector(MeterRegistry meterRegistry,
                                  @Qualifier("batchResumeParseExecutor") ThreadPoolTaskExecutor executor,
                                  RedisTemplate<String, Object> redisTemplate,
                                  DataSource dataSource) {
        this.meterRegistry = meterRegistry;

        // 初始化业务指标
        this.resumeParseCounter = Counter.builder("resume.parse.total")
            .description("简历解析总数")
            .register(meterRegistry);

        this.resumeParseTimer = Timer.builder("resume.parse.duration")
            .description("简历解析耗时")
            .register(meterRegistry);

        this.fileSizeDistribution = DistributionSummary.builder("resume.file.size.bytes")
            .description("简历文件大小分布")
            .minimumExpectedValue(1024L)
            .maximumExpectedValue(50 * 1024 * 1024L)
            .register(meterRegistry);

        this.userOperationCounter = Counter.builder("user.operation.total")
            .description("用户操作总数")
            .register(meterRegistry);

        this.userQueryTimer = Timer.builder("user.query.duration")
            .description("用户查询耗时")
            .register(meterRegistry);

        // 系统资源指标
        this.threadPoolActiveThreads = Gauge.builder("threadpool.active.threads")
            .description("线程池活跃线程数")
            .register(meterRegistry, executor, ThreadPoolTaskExecutor::getActiveCount);

        this.redisConnectionUsage = Gauge.builder("redis.connection.usage")
            .description("Redis连接使用情况")
            .register(meterRegistry, this, BusinessMetricsCollector::getRedisConnectionCount);

        this.databaseConnectionUsage = Gauge.builder("database.connection.usage")
            .description("数据库连接使用情况")
            .register(meterRegistry, dataSource, this::getDatabaseConnectionCount);
    }

    /**
     * 记录简历解析指标
     */
    public void recordResumeParseMetrics(String status, long duration, long fileSize, String fileType) {
        resumeParseCounter.increment(
            Tags.of("status", status, "file_type", fileType)
        );
        resumeParseTimer.record(duration, TimeUnit.MILLISECONDS);
        fileSizeDistribution.record(fileSize);
    }

    /**
     * 记录用户操作指标
     */
    public void recordUserOperation(String operation, String result, long duration) {
        userOperationCounter.increment(
            Tags.of("operation", operation, "result", result)
        );

        if (operation.contains("query") || operation.contains("search")) {
            userQueryTimer.record(duration, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 记录API调用指标
     */
    public void recordApiCall(String apiName, String status, long duration) {
        Counter.builder("api.call.total")
            .tag("api", apiName)
            .tag("status", status)
            .register(meterRegistry)
            .increment();

        Timer.builder("api.call.duration")
            .tag("api", apiName)
            .register(meterRegistry)
            .record(duration, TimeUnit.MILLISECONDS);
    }

    /**
     * 记录缓存操作指标
     */
    public void recordCacheOperation(String operation, String result) {
        Counter.builder("cache.operation.total")
            .tag("operation", operation)
            .tag("result", result)
            .register(meterRegistry)
            .increment();
    }

    private double getRedisConnectionCount() {
        // 获取Redis连接数的逻辑
        // 这里需要根据实际的Redis配置来实现
        return 0.0;
    }

    private double getDatabaseConnectionCount(DataSource dataSource) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            return hikariDataSource.getHikariPoolMXBean().getActiveConnections();
        }
        return 0.0;
    }

    /**
     * 定期记录系统状态
     */
    @Scheduled(fixedDelay = 60000) // 每分钟记录一次
    public void recordSystemMetrics() {
        // JVM内存使用情况
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();

        Gauge.builder("jvm.memory.heap.usage.ratio")
            .register(meterRegistry, heapUsage, usage ->
                (double) usage.getUsed() / usage.getMax())
            .value();

        // GC信息
        ManagementFactory.getGarbageCollectorMXBeans().forEach(gcBean -> {
            Gauge.builder("jvm.gc.collection.count")
                .tag("gc", gcBean.getName())
                .register(meterRegistry, gcBean, bean -> bean.getCollectionCount())
                .value();
        });
    }
}

### 6.2 健康检查和告警策略

基于Spring Boot Actuator的健康检查增强：

```java
@Component
@Slf4j
public class CustomHealthIndicators {

    private final ThirdPartyResumeParseService parseService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DataSource dataSource;

    public CustomHealthIndicators(ThirdPartyResumeParseService parseService,
                                RedisTemplate<String, Object> redisTemplate,
                                DataSource dataSource) {
        this.parseService = parseService;
        this.redisTemplate = redisTemplate;
        this.dataSource = dataSource;
    }

    /**
     * 第三方API健康检查
     */
    @Component("thirdPartyApiHealth")
    public class ThirdPartyApiHealthIndicator implements HealthIndicator {

        @Override
        public Health health() {
            try {
                // 简单的健康检查调用
                boolean isHealthy = checkThirdPartyApiHealth();

                if (isHealthy) {
                    return Health.up()
                        .withDetail("api", "third-party-resume-parse")
                        .withDetail("status", "UP")
                        .withDetail("lastCheck", Instant.now())
                        .build();
                } else {
                    return Health.down()
                        .withDetail("api", "third-party-resume-parse")
                        .withDetail("status", "DOWN")
                        .withDetail("lastCheck", Instant.now())
                        .build();
                }
            } catch (Exception e) {
                return Health.down()
                    .withDetail("api", "third-party-resume-parse")
                    .withDetail("error", e.getMessage())
                    .withDetail("lastCheck", Instant.now())
                    .build();
            }
        }

        private boolean checkThirdPartyApiHealth() {
            // 实现简单的健康检查逻辑
            // 可以是一个轻量级的ping请求
            return true; // 简化实现
        }
    }

    /**
     * Redis连接健康检查
     */
    @Component("redisHealth")
    public class RedisHealthIndicator implements HealthIndicator {

        @Override
        public Health health() {
            try {
                // 执行简单的Redis操作
                redisTemplate.opsForValue().set("health:check", "ok", Duration.ofSeconds(10));
                String result = (String) redisTemplate.opsForValue().get("health:check");

                if ("ok".equals(result)) {
                    return Health.up()
                        .withDetail("redis", "connection-ok")
                        .withDetail("lastCheck", Instant.now())
                        .build();
                } else {
                    return Health.down()
                        .withDetail("redis", "connection-failed")
                        .withDetail("lastCheck", Instant.now())
                        .build();
                }
            } catch (Exception e) {
                return Health.down()
                    .withDetail("redis", "connection-error")
                    .withDetail("error", e.getMessage())
                    .withDetail("lastCheck", Instant.now())
                    .build();
            }
        }
    }

    /**
     * 数据库连接健康检查
     */
    @Component("databaseHealth")
    public class DatabaseHealthIndicator implements HealthIndicator {

        @Override
        public Health health() {
            try (Connection connection = dataSource.getConnection()) {
                // 执行简单的查询
                try (PreparedStatement statement = connection.prepareStatement("SELECT 1")) {
                    ResultSet resultSet = statement.executeQuery();
                    if (resultSet.next() && resultSet.getInt(1) == 1) {
                        return Health.up()
                            .withDetail("database", "connection-ok")
                            .withDetail("lastCheck", Instant.now())
                            .build();
                    }
                }

                return Health.down()
                    .withDetail("database", "query-failed")
                    .withDetail("lastCheck", Instant.now())
                    .build();

            } catch (Exception e) {
                return Health.down()
                    .withDetail("database", "connection-error")
                    .withDetail("error", e.getMessage())
                    .withDetail("lastCheck", Instant.now())
                    .build();
            }
        }
    }
}

/**
 * 告警事件处理
 */
@Component
@Slf4j
public class AlertEventHandler {

    private final MeterRegistry meterRegistry;

    public AlertEventHandler(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    /**
     * 处理健康检查失败事件
     */
    @EventListener
    public void handleHealthCheckFailure(HealthCheckFailureEvent event) {
        log.warn("健康检查失败: component={}, error={}",
                event.getComponent(), event.getError());

        meterRegistry.counter("health.check.failure",
            "component", event.getComponent()).increment();

        // 根据组件类型采取不同的处理策略
        switch (event.getComponent()) {
            case "thirdPartyApi":
                handleThirdPartyApiFailure(event);
                break;
            case "redis":
                handleRedisFailure(event);
                break;
            case "database":
                handleDatabaseFailure(event);
                break;
        }
    }

    private void handleThirdPartyApiFailure(HealthCheckFailureEvent event) {
        // 第三方API失败处理
        log.warn("第三方API健康检查失败，启用降级模式");
        // 可以设置熔断器状态或启用缓存模式
    }

    private void handleRedisFailure(HealthCheckFailureEvent event) {
        // Redis失败处理
        log.error("Redis连接失败，系统性能可能受影响");
        // 可以切换到本地缓存或降级处理
    }

    private void handleDatabaseFailure(HealthCheckFailureEvent event) {
        // 数据库失败处理
        log.error("数据库连接失败，这是严重问题");
        // 应该立即通知运维人员
    }
}

/**
 * 健康检查失败事件
 */
public class HealthCheckFailureEvent {
    private final String component;
    private final String error;
    private final Instant timestamp;

    public HealthCheckFailureEvent(String component, String error) {
        this.component = component;
        this.error = error;
        this.timestamp = Instant.now();
    }

    // Getters
    public String getComponent() { return component; }
    public String getError() { return error; }
    public Instant getTimestamp() { return timestamp; }
}

## 七、分步实施计划

### 第一阶段：基础设施搭建（1-2周）
**目标**: 引入RocketMQ和OSS，建立基础架构

1. **RocketMQ集成**
   - 添加RocketMQ依赖和配置
   - 实现生产者和消费者基础框架
   - 配置主题和消费组
   - **风险**: 中等，需要RocketMQ环境搭建
   - **预期收益**: 建立异步处理基础

2. **阿里云OSS集成**
   - 配置OSS客户端和连接池
   - 实现基础的上传下载功能
   - 配置存储桶和访问权限
   - **风险**: 低，OSS服务稳定
   - **预期收益**: 解决文件存储扩展性问题

3. **线程池配置优化**
   - 整合现有线程池配置
   - 新增OSS专用线程池
   - 添加线程池监控指标
   - **风险**: 低，主要是配置调整
   - **预期收益**: 减少20-30%的线程上下文切换开销

### 第二阶段：核心功能实现（2-3周）
**目标**: 实现基于消息队列的异步处理流程

1. **消息队列异步处理**
   - 实现简历解析消息生产和消费
   - 添加消息重试和死信队列机制
   - 实现消息限流和背压控制
   - **风险**: 中等，需要充分测试消息可靠性
   - **预期收益**: 提升50-70%的并发处理能力

2. **OSS智能存储策略**
   - 实现分片上传和断点续传
   - 添加文件去重和缓存机制
   - 实现自动清理和生命周期管理
   - **风险**: 中等，需要处理大文件上传
   - **预期收益**: 支持大文件处理，减少存储成本

3. **数据库批量操作优化**
   - 优化MyBatis Plus批量操作
   - 实现智能分批处理
   - 添加数据库连接池监控
   - **风险**: 中等，需要数据库性能测试
   - **预期收益**: 提升30-50%的数据写入性能

### 第三阶段：高级特性和优化（2-3周）
**目标**: 实现高可用和性能优化

1. **熔断器和降级策略**
   - 实现第三方API熔断器
   - 添加OSS和MQ的降级策略
   - 实现缓存降级机制
   - **风险**: 中等，需要仔细设计降级逻辑
   - **预期收益**: 提升系统容错能力

2. **高级监控和告警**
   - 实现RocketMQ和OSS监控指标
   - 添加业务指标和告警规则
   - 集成现有Prometheus监控体系
   - **风险**: 低，主要是监控增强
   - **预期收益**: 减少80%的故障响应时间

3. **性能调优和压测**
   - 进行端到端性能测试
   - 优化消息队列和OSS配置
   - 调优JVM和数据库参数
   - **风险**: 低，主要是参数调优
   - **预期收益**: 整体性能提升30-50%

### 实施注意事项

#### 向后兼容性
- 所有优化都保持API向后兼容
- 配置项采用渐进式迁移
- 保留原有功能作为备选方案

#### 测试策略
- 每个阶段都需要完整的单元测试和集成测试
- 性能测试验证优化效果
- 灰度发布验证生产环境稳定性

#### 回滚计划
- 每个优化都有明确的回滚方案
- 关键配置支持动态调整
- 监控指标支持快速问题定位

#### 资源需求
- **开发资源**: 1-2名开发人员
- **测试资源**: 需要性能测试环境
- **运维资源**: 需要监控和部署支持

### 预期整体收益

1. **架构升级收益**
   - 引入消息队列，实现真正的异步解耦
   - 集成云存储，解决文件存储扩展性问题
   - 建立完善的监控和告警体系

2. **性能提升**
   - 批量处理性能提升50-80%（消息队列异步处理）
   - 文件上传性能提升60%+（OSS分片上传）
   - 系统并发能力提升3-5倍
   - 资源利用率提升30-40%

3. **稳定性提升**
   - 系统可用性提升至99.9%+
   - 故障恢复时间减少80%
   - 支持大文件处理（100MB+）
   - 消息可靠性保证（重试+死信队列）

4. **扩展性提升**
   - 支持水平扩展（多实例消费）
   - 存储容量无限扩展（OSS）
   - 消息队列支持削峰填谷
   - 微服务架构准备

5. **运维效率**
   - 监控覆盖率提升至95%+
   - 故障定位时间减少70%
   - 自动化程度提升60%
   - 支持灰度发布和回滚

## 技术债务和风险控制

### 主要技术债务
1. **依赖外部服务**: RocketMQ和OSS的可用性依赖
2. **复杂性增加**: 分布式系统的复杂性
3. **成本增加**: OSS存储和流量成本

### 风险控制措施
1. **降级策略**: 每个外部依赖都有降级方案
2. **监控告警**: 完善的监控和自动告警
3. **测试覆盖**: 充分的单元测试和集成测试
4. **文档完善**: 详细的运维文档和故障处理手册

这个优化方案将用户中心系统从单体架构升级为具备分布式特性的高性能系统，为后续的微服务拆分奠定基础。

## 附录：必要的依赖和配置

### A.1 Maven依赖补充

```xml
<!-- RocketMQ Spring Boot Starter -->
<dependency>
    <groupId>org.apache.rocketmq</groupId>
    <artifactId>rocketmq-spring-boot-starter</artifactId>
    <version>2.2.3</version>
</dependency>

<!-- 阿里云OSS SDK -->
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.17.4</version>
</dependency>

<!-- Resilience4j 熔断器 -->
<dependency>
    <groupId>io.github.resilience4j</groupId>
    <artifactId>resilience4j-spring-boot3</artifactId>
    <version>2.1.0</version>
</dependency>

<!-- Google Guava (RateLimiter) -->
<dependency>
    <groupId>com.google.guava</groupId>
    <artifactId>guava</artifactId>
    <version>32.1.3-jre</version>
</dependency>

<!-- Caffeine Cache -->
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
    <version>3.1.8</version>
</dependency>
```

### A.2 消息实体类

```java
// 简历解析消息
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResumeParseMessage implements Serializable {
    private String messageId;
    private String batchId;
    private String fileName;
    private String ossKey;
    private String fileType;
    private Long fileSize;
    private Integer priority; // 优先级：1-高，2-中，3-低
    private Long userId;
    private LocalDateTime createTime;
    private Map<String, Object> metadata;
}

// 文件上传消息
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadMessage implements Serializable {
    private String messageId;
    private String fileName;
    private String ossKey;
    private String fileType;
    private Long fileSize;
    private String uploadStatus;
    private Long userId;
    private LocalDateTime uploadTime;
}
```

### A.3 OSS专用线程池配置

```java
@Configuration
public class OSSThreadPoolConfig {

    @Bean("ossUploadExecutor")
    public ThreadPoolTaskExecutor ossUploadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(300);
        executor.setThreadNamePrefix("OSS-Upload-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}
```

### A.4 完整的application.yml配置示例

```yaml
# RocketMQ配置
rocketmq:
  name-server: 127.0.0.1:9876
  producer:
    group: user-center-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    max-message-size: 4194304
  consumer:
    group: user-center-consumer-group
    consume-thread-min: 5
    consume-thread-max: 20

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    internal-endpoint: https://oss-cn-hangzhou-internal.aliyuncs.com
    access-key-id: ${ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
    bucket-name: user-center-files
    connection-timeout: 3000
    socket-timeout: 30000
    max-connections: 50
    max-error-retry: 3

# 线程池配置
async:
  task:
    executor:
      core-pool-size: 5
      max-pool-size: 15
      queue-capacity: 200
      keep-alive-seconds: 60
      thread-name-prefix: "AsyncTask-"
      batch-resume-parse-executor:
        core-pool-size: 3
        max-pool-size: 8
        queue-capacity: 100
        keep-alive-seconds: 300
        thread-name-prefix: "BatchResumeParser-"

# 熔断器配置
resilience4j:
  circuitbreaker:
    instances:
      third-party-api:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 60s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 10s
```